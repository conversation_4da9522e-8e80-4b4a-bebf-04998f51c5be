<script setup lang="ts">
import dayjs from 'dayjs'

defineOptions({
    name: 'UserCheckinIndex',
})

definePageMeta({
    meta: {
        tabbar: true,
        layout: {
            customBg: 'bg-#F4F5F7 overflow-auto',
        },
    },
})

interface NewCheckinData {
    details: Array<{
        kind: string
        result: number
    }>
}

const { fetchArchivesStatus } = useBadgeStore()
const { startGuide, destroyGuide } = useGuide()
const startTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const showCenter = ref(false)

const selectedDate = ref(dayjs().format('YYYY-MM-DD'))
const dietRef = ref()
const waterIntakeNum = ref(0)
const waistCircumferenceNum = ref(0)
const stepNum = ref(0)
const planId = ref<number | null>(null)

async function getDailyStats(date: string) {
    try {
        const { results } = await useWrapFetch<BaseResponse<NewCheckinData>>('/checkInCustomerData/getDailyStats', {
            method: 'POST',
            body: {
                checkInDate: date,
            },
        })

        waterIntakeNum.value = results?.details.find(item => item.kind === 'waterIntake')?.result || 0
        waistCircumferenceNum.value = results?.details.find(item => item.kind === 'waistCircumference')?.result || 0
        stepNum.value = results?.details.find(item => item.kind === 'sport')?.result || 0
    } catch (error) {
        console.error('获取每日统计数据失败:', error)
    }
}

const { saveWeightManagementData } = useWeightManagement()

const reportGenerating = ref(localStorage.getItem('reportGenerating') === '1')
const isComponentAlive = ref(true)

const { isPolling, startPolling, stopPolling } = useCancellablePolling({
    interval: 10000,
    maxAttempts: 20,
    onError: (error, attempt) => {
        console.warn(`获取健康计划信息失败 (第${attempt}次):`, error)
    },
})

async function getHealthProgramIndexLoop() {
    if (isPolling.value) return

    const result = await startPolling<BaseResponse<HealthProgramIndex>>(
        '/api/healthProgram/getHealthProgramIndex',
        { method: 'GET' },
        (response) => {
            // 轮询条件：当报告生成完成时停止
            return response.results?.qwenReportEnd === 1
        },
    )

    if (result?.results) {
        const results = result.results

        if (!isComponentAlive.value) return

        const returnData = await saveWeightManagementData({
            healthProgramData: results,
            dietMode: results.dietPlan?.dietMode || '地中海饮食模式',
            targetWeight: JSON.parse(results.weightManagementQuestionnaire).height - 105,
            weightCheckIn: JSON.parse(results.weightManagementQuestionnaire)?.weight,
        })

        if (returnData?.[0]?.status === 'fulfilled') {
            planId.value = returnData[0].value?.results
        }

        localStorage.removeItem('reportGenerating')
        showCenter.value = true
    }
}

function startReportCheckInterval() {
    getHealthProgramIndexLoop()
}

function clearReportCheckInterval() {
    stopPolling()
}

watch(selectedDate, () => {
    getDailyStats(selectedDate.value)
    dietRef.value?.refreshMeal()
})

onBeforeMount(async () => {
    try {
        await getDailyStats(selectedDate.value)
    } catch (error) {
        console.error(error)
    }
})

onMounted(() => {
    const hasSeenGuide = localStorage.getItem('hasSeenGuide')
    if (!hasSeenGuide) {
        startTimer.value = setTimeout(() => {
            startGuide()
        }, 1000)
    }
})

onBeforeUnmount(() => {
    if (startTimer.value) {
        clearTimeout(startTimer.value)
    }
    isComponentAlive.value = false
    clearReportCheckInterval()
    destroyGuide()
})

if (reportGenerating.value) {
    startReportCheckInterval()
} else {
    fetchArchivesStatus()
}

async function handleCheckNow() {
    try {
        if (planId.value) {
            await useWrapFetch(`/api/healthProgram/updateReadStatus/${planId.value}/1`, {
                method: 'POST',
            })
            navigateTo(`/user/external/weight-management-plan?planId=${planId.value}`)
        }

        showCenter.value = false
    } catch (error) {
        console.error(error)
    }
}

async function handleCheckLater() {
    showCenter.value = false
    await fetchArchivesStatus()
}
</script>

<template>
    <div class="checkin-container px-16px pt-16px flex flex-col gap-8px overflow-y-auto overflow-x-hidden">
        <user-checkin-health-manage-calendar
            v-model="selectedDate"
            checkin-type="mealCount"
        />

        <user-checkin-health-manage-diet
            ref="dietRef"
            :selected-date="selectedDate"
            :water-intake-num="waterIntakeNum"
            :step-num="stepNum"
            :waist-circumference-num="waistCircumferenceNum"
            @update:step-num="stepNum = $event"
        />

        <shared-safe-buttom base="60px" />

        <van-popup v-model:show="showCenter" round :style="{ padding: '30px' }" :close-on-click-overlay="false" class="checkin-center-popup">
            <div class="w-255px flex flex-col items-center">
                <img src="@/assets/images/checkin/diet/popup-plan.png" width="167px" height="32px" alt="" srcset="" />
                <div class="text-t-5 text-14px">
                    根据您的问卷及检查检验结果，医生已开具针对性体重管理健康评估报告
                </div>
                <img src="@/assets/images/checkin/diet/popup-example.png" width="255px" height="83px" class="my-16px" alt="" srcset="" />
                <van-button type="primary" round class="w-255px! h-50px!" @click="handleCheckNow">
                    立即查看
                </van-button>
                <div class="w-255px text-#868F9C mt-8px text-14px font-400 text-center" @click="handleCheckLater">
                    稍后查看
                </div>
            </div>
        </van-popup>

        <user-sign-overlay />
    </div>
</template>

<style scoped>
.checkin-center-popup {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    background:
        linear-gradient(215.92deg, rgba(42, 211, 183, 0.5) -1.28%, rgba(255, 255, 255, 0.5) 55.03%),
        linear-gradient(145.07deg, rgba(166, 191, 255, 0.5) -3.55%, rgba(255, 255, 255, 0.5) 36.78%),
        #FFFFFF;  /* 底层白色背景 */
    box-shadow: 0px -3px 4px 0px #FFFFFF40 inset, 0px 2px 7px 0px #0000001F;
}
</style>
