<script setup lang="ts">
import dayjs from 'dayjs'

import { CHECKIN_TOOLS } from '@/utils/checkinCard'
import { calculateContinuousCheckinDays, canShowEncourageToday, computeWeightWeekStatus, getBmiText, markEncourageShownToday } from '@/utils/common'
import CalorieProgress from './calorie-progress.vue'
import CalorieIntakeSettings from './calorie-intake-settings.vue'
import TimeRangeChart from './time-range-chart.vue'

interface NutritionItem {
    carbohydrates?: number
    protein?: number
    fat?: number
    dietaryFiber?: number
    [key: string]: number | undefined
}

interface ChartData {
    labels: string[]
    data: number[]
}

interface WeightChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const props = defineProps<{
    selectedDate: string
    waterIntakeNum: number
    stepNum: number
    waistCircumferenceNum: number
}>()

const emit = defineEmits<{
    'update:stepNum': [value: number]
}>()

// Composables
const { fastingState, fastingText, fastingStatus } = useFasting()
const { passedTime, progress: progressValue, setTime } = useTimer()
const { latestHealthProgramPlan } = storeToRefs(useBadgeStore())
const { userInfo } = storeToRefs(useUserStore())

// 饮食相关数据
const archiveResults = ref<Archives>()
const showStillEat = ref(false)
const stepCalorieEnabled = ref(false)
const recommendKcal = ref<number>(0)
const isHasCustomed = ref(false)
const loadingStillEat = ref(false)
const caloricIntake = ref<number>(0)
const isInitialized = ref(false)

// 体重相关数据
const timeRangeChartRef = ref<InstanceType<typeof TimeRangeChart>>()
const showInitialWeightSheet = ref(false)
const showWeightTargetSheet = ref(false)
const showWeightSheet = ref(false)
const diaryNum = ref<number>(0)
const showWeightLossTarget = ref(false)
const weightLossTarget = ref(3)
const relevantRecordWeight = ref<number>(0)
const isWeightInitialized = ref(false)
const showEncouragePopup = ref(false)
const weightWeekStatus = ref<boolean[]>([])
const continuousCheckinCount = ref(0)

// 运动相关数据
const isSportInitialized = ref(false)
const allSportData = ref<any[]>([])
const allStepData = ref<any[]>([])
const isSportDataLoaded = ref(false)
const isLoadingSportData = ref(false)

// API调用
const { data: sportData } = useAPI<any>('/checkInCustomerSport/list', {
    method: 'POST',
    body: computed(() => ({
        checkInDate: props.selectedDate,
    })),
})

const { data: initialWeight, refresh: refreshInitialWeight } = useAPI<string>('/user/getInitialWeight')
const { data: customerIndexResults, refresh: refreshCustomerIndex } = useAPI<{ monthlyWeightIndex: string, weightIndex: string }>('/checkInCustomerIndex/get', { method: 'post', body: {} })

// 图表数据
const sportChartData = reactive<{
    周: ChartData
    月: ChartData
    年: ChartData
}>({
    周: { labels: [], data: [] },
    月: { labels: [], data: [] },
    年: { labels: [], data: [] },
})

const stepChartData = reactive<{
    周: ChartData
    月: ChartData
    年: ChartData
}>({
    周: { labels: [], data: [] },
    月: { labels: [], data: [] },
    年: { labels: [], data: [] },
})

const { data: mealListData, refresh: refreshMeal } = useAPI<any>('/checkInCustomerMeal/list', {
    method: 'POST',
    body: computed(() => ({
        checkInDate: props.selectedDate,
    })),
})

// 体重相关计算属性
const initialWeightNum = computed(() => Number(initialWeight.value?.results) || 0)
const weightAndHeight = computed(() => {
    const data = (archiveResults.value || {}) as { archiveWeight: string, archiveHeight: string }
    return {
        weight: data.archiveWeight || '',
        height: data.archiveHeight || '',
    }
})
const targetWeightNum = computed(() => {
    const data = (customerIndexResults.value?.results || {}) as { weightIndex: string }
    return Number(data.weightIndex) || 0
})

// BMI相关计算
const bmiStatus = computed(() => {
    if (targetWeightNum.value > Number(archiveResults.value?.archiveWeight)) {
        return '正常体重'
    }

    // 优先使用档案BMI，备选从健康计划问卷获取
    const questionnaire = latestHealthProgramPlan.value?.weightManagementQuestionnaire
    const bmi = archiveResults.value?.archiveBmi
        || (questionnaire
            ? (() => {
                    try {
                        return JSON.parse(questionnaire)?.bmi
                    } catch {
                        return null
                    }
                })()
            : null)

    return getBmiText(bmi)
})

const bmiTip = computed(() => {
    const textMap = {
        低体重: '不建议减重',
        正常体重: '建议维持',
        超重: '本月目标',
        肥胖: '本月目标',
        重度肥胖: '本月目标',
    }
    return textMap[bmiStatus.value as keyof typeof textMap]
})

const computedWeight = computed(() => {
    if (relevantRecordWeight.value) {
        if (!['不建议减重', '建议维持'].includes(bmiTip.value)) {
            return (Number(relevantRecordWeight.value) || Number(weightAndHeight.value?.weight))
        }
    }
    return 0
})

const endVal = computed(() => computedWeight.value - weightLossTarget.value)

const bmiRangeText = computed(() => {
    const questionnaire = latestHealthProgramPlan.value?.weightManagementQuestionnaire
    if (!questionnaire) return '18.5-23.9'

    try {
        const parsedData = JSON.parse(questionnaire)
        const age = parsedData?.age || 18
        const bmiRanges = [
            { max: 65, text: '18.5-23.9' }, // 18-65岁
            { max: 80, text: '20-26.9' }, // 65-80岁
            { max: Infinity, text: '22-26.9' }, // 80岁以上
        ]
        return bmiRanges.find(range => age < range.max)?.text || '18.5-23.9'
    } catch {
        return '18.5-23.9'
    }
})

const weightChartData = reactive<WeightChartData>({
    周: { labels: [], data: [] },
    月: { labels: [], data: [] },
    年: { labels: [], data: [] },
})

const allWeightData = ref<any[]>([])
const isWeightDataLoaded = ref(false)
const isLoadingWeightData = ref(false)

// 饮食相关计算属性
const mealIntakeNum = computed(() => {
    if (!mealListData.value?.results?.length) return 0
    return mealListData.value.results.reduce((total: number, item: { calorie?: number }) => {
        return total + (item.calorie || 0)
    }, 0)
})

const stillEatNum = computed(() => {
    const sportKcal = sportData.value?.results?.allKcal ?? 0
    const stepKcal = stepCalorieEnabled.value ? (props?.stepNum || 0) * (Number(archiveResults.value?.archiveWeight || 0) / 2000) : 0
    const result = (recommendKcal.value + sportKcal * 0.9) - mealIntakeNum.value + stepKcal
    return Math.round(result)
})

const progressPercent = computed<number>(() => {
    const sportKcal = sportData.value?.results?.allKcal ?? 0
    if (recommendKcal.value + sportKcal * 0.9 === 0) return 0
    return mealIntakeNum.value / (recommendKcal.value + sportKcal * 0.9)
})

// 运动相关计算属性
const totalSportTime = computed(() => {
    if (!sportData.value?.results?.detailList?.length) return 0
    return sportData.value.results.detailList.reduce((total: number, item: { subList?: Array<{ sportTime?: number }> }) => {
        return total + (item.subList?.reduce((subTotal: number, subItem: { sportTime?: number }) => {
            return subTotal + (subItem.sportTime || 0)
        }, 0) || 0)
    }, 0)
})

const sportConsume = computed(() => {
    return (sportData.value?.results?.allKcal || 0) + ((props?.stepNum || 0) * (Number(archiveResults.value?.archiveWeight || 0) / 2000))
})

// 工具卡片
const activityCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'activity'))
const diaryCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'diary'))
const waistCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'waistCircumference'))
const weightCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'weight'))
const nutritionList = ref([
    {
        title: '碳水化合物',
        key: 'carbohydrates',
        current: 0,
        total: 0,
        unit: 'g',
        gradient: {
            from: '#00AC97',
            to: '#52D2C2',
        },
    },
    {
        title: '蛋白质',
        key: 'protein',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#3DB5FF',
            to: '#7ECEFF',
        },
    },
    {
        title: '脂肪',
        key: 'fat',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#FFB348',
            to: '#FFD191',
        },
    },
    {
        title: '膳食纤维',
        key: 'dietaryFiber',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#30DD4A',
            to: '#80F692',
        },
    },
])

// 饮食相关方法
function getCaloricIntake() {
    try {
        const computedDietPlanKcal = (Number(archiveResults.value?.archiveHeight || 0) - 105) * 25
        if (!isHasCustomed.value) {
            recommendKcal.value = computedDietPlanKcal
        }
        caloricIntake.value = computedDietPlanKcal
    } catch (error) {
        console.error('计算推荐热量失败:', error)
    }
}

// 图表配置
const timeRangeConfig = {
    周: { days: 7, label: 'M月D日' },
    月: { days: 30, label: 'M月D日' },
    年: { days: 365, label: 'M月D日' },
} as const

// 体重相关方法
function findRelevantWeightRecord(results: any[]) {
    if (!Array.isArray(results) || results.length === 0) return null

    const currentMonthFirstDay = dayjs().startOf('month').format('YYYY-MM-DD')

    const firstDayRecord = results.find(item => item.checkInDate === currentMonthFirstDay)
    if (firstDayRecord) return firstDayRecord?.weight

    const laterRecords = results.filter(item => item.checkInDate > currentMonthFirstDay)
    if (laterRecords.length > 0) {
        return laterRecords.sort((a, b) =>
            dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
        )[0]?.weight
    }

    const earlierRecords = results.filter(item => item.checkInDate < currentMonthFirstDay)
    if (earlierRecords.length > 0) {
        return earlierRecords.sort((a, b) =>
            dayjs(b.checkInDate).diff(dayjs(a.checkInDate)),
        )[0]?.weight
    }

    return null
}

function processWeightDataByTimeRange(
    allData: any[],
    tab: '周' | '月' | '年',
): ChartData {
    const config = timeRangeConfig[tab]
    const cutoffDate = dayjs().subtract(config.days, 'day')

    const filteredData = allData
        .filter(item => dayjs(item.checkInDate).isAfter(cutoffDate))
        .sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))

    const labels = filteredData.map(item =>
        dayjs(item.checkInDate).format(config.label),
    )
    const data = filteredData.map(item => Number(item.weight))

    return { labels, data }
}

function processDataByTimeRange(
    allData: any[],
    tab: '周' | '月' | '年',
    valueKey: string,
): ChartData {
    const config = timeRangeConfig[tab]
    const cutoffDate = dayjs().subtract(config.days, 'day')

    const filteredData = allData
        .filter(item => dayjs(item.checkInDate).isAfter(cutoffDate))
        .sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))

    const labels = filteredData.map(item =>
        dayjs(item.checkInDate).format(config.label),
    )
    const data = filteredData.map(item => Number(item[valueKey]))

    return { labels, data }
}

async function fetchAllWeightData() {
    if (isWeightDataLoaded.value || isLoadingWeightData.value) return

    isLoadingWeightData.value = true
    const startDate = '2024-08-19' // 项目开始日期
    const endDate = dayjs().format('YYYY-MM-DD')

    try {
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerWeight/getWeightTrendData', {
            method: 'post',
            body: {
                startDate,
                endDate,
            },
        })

        if (Array.isArray(results)) {
            allWeightData.value = results.sort((a, b) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )

            if (canShowEncourageToday('weight', userInfo.value?.phone)) {
                // 计算本周（周日-周六，包含今天）的打卡状态
                weightWeekStatus.value = computeWeightWeekStatus(results)
                continuousCheckinCount.value = calculateContinuousCheckinDays(results, startDate)
            }

            relevantRecordWeight.value = findRelevantWeightRecord(results)
            isWeightDataLoaded.value = true
        } else {
            // 处理空数据情况
            allWeightData.value = []
            isWeightDataLoaded.value = true
        }
    } catch (error) {
        console.error('获取体重数据失败:', error)
        showToast('获取体重数据失败，请重试')
        isWeightDataLoaded.value = false
    } finally {
        isLoadingWeightData.value = false
    }
}

async function getWeightTrendData(tab: '周' | '月' | '年') {
    if (!isWeightDataLoaded.value) {
        await fetchAllWeightData()
    }

    if (allWeightData.value.length > 0) {
        weightChartData[tab] = processWeightDataByTimeRange(allWeightData.value, tab)
    }
}

function initializeWeightChartData() {
    const timeRanges = ['周', '月', '年'] as const
    timeRanges.forEach((timeRange) => {
        if (allWeightData.value.length > 0) {
            weightChartData[timeRange] = processWeightDataByTimeRange(allWeightData.value, timeRange)
        }
    })
}

async function getDiaryCount() {
    try {
        const { results } = await useWrapFetch<BaseResponse<number>>('/diary/count')
        diaryNum.value = results || 0
    } catch (error) {
        console.error('获取日记数量失败:', error)
    }
}

function handleShowWeightLossTarget() {
    const data = (customerIndexResults.value?.results || {}) as { weightIndex: string }
    const weightIndex = Number(data?.weightIndex)
    if (!weightIndex) {
        showToast('请先设置体重目标')
        return
    }

    if (!['不建议减重', '建议维持'].includes(bmiTip.value)) {
        showWeightLossTarget.value = true
    }
}

async function handleWeightLossTargetConfirm(data: { weightLossTarget: number, targetWeight: number }) {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            monthlyWeightIndex: data.weightLossTarget,
            weightIndex: data.targetWeight,
        } })
        weightLossTarget.value = data.weightLossTarget
    } catch {
        showFailToast('保存失败')
    }
    showWeightLossTarget.value = false
    refreshCustomerIndex()
}

function handleWeightLossTargetCancel() {
    showWeightLossTarget.value = false
}

async function refreshWeightData() {
    allWeightData.value = []
    isWeightDataLoaded.value = false

    await fetchAllWeightData()
    initializeWeightChartData()
}

async function initWeightData() {
    if (isWeightInitialized.value) return

    await Promise.allSettled([
        getDiaryCount(),
        fetchAllWeightData(),
    ])

    initializeWeightChartData()
    isWeightInitialized.value = true
}

// 运动相关方法
async function fetchAllSportData() {
    if (isSportDataLoaded.value || isLoadingSportData.value) return

    isLoadingSportData.value = true
    const endDate = dayjs().format('YYYY-MM-DD')
    const startDate = dayjs().subtract(365, 'day').format('YYYY-MM-DD')

    try {
        const [sportResponse, stepResponse] = await Promise.all([
            useWrapFetch<BaseResponse<any[]>>('/checkInCustomerSport/getSportTrendData', {
                method: 'post',
                body: { startDate, endDate },
            }),
            useWrapFetch<BaseResponse<any[]>>('/checkInCustomerSport/getFootTrendData', {
                method: 'post',
                body: { startDate, endDate },
            }),
        ])

        if (Array.isArray(sportResponse.results)) {
            allSportData.value = sportResponse.results.sort((a, b) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )
        }

        if (Array.isArray(stepResponse.results)) {
            allStepData.value = stepResponse.results.sort((a, b) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )
        }

        isSportDataLoaded.value = true
    } catch (error) {
        console.error('获取运动数据失败:', error)
    } finally {
        isLoadingSportData.value = false
    }
}

async function getSportTrendData(tab: '周' | '月' | '年') {
    if (!isSportDataLoaded.value) {
        await fetchAllSportData()
    }

    if (allSportData.value.length > 0) {
        sportChartData[tab] = processDataByTimeRange(allSportData.value, tab, 'kcal')
    }
}

async function getStepsTrendData(tab: '周' | '月' | '年') {
    if (!isSportDataLoaded.value) {
        await fetchAllSportData()
    }

    if (allStepData.value.length > 0) {
        stepChartData[tab] = processDataByTimeRange(allStepData.value, tab, 'steps')
    }
}

function updateChartDataForTimeRange(timeRange: '周' | '月' | '年') {
    if (allSportData.value.length > 0) {
        sportChartData[timeRange] = processDataByTimeRange(allSportData.value, timeRange, 'kcal')
    }
    if (allStepData.value.length > 0) {
        stepChartData[timeRange] = processDataByTimeRange(allStepData.value, timeRange, 'steps')
    }
}

function initializeSportChartData() {
    const timeRanges = ['周', '月', '年'] as const
    timeRanges.forEach(updateChartDataForTimeRange)
}

async function initSportData() {
    if (isSportInitialized.value) return

    await fetchAllSportData()
    initializeSportChartData()
    isSportInitialized.value = true
}

async function refreshSportData() {
    allSportData.value = []
    allStepData.value = []
    isSportDataLoaded.value = false

    await fetchAllSportData()
    initializeSportChartData()
}

async function handleClickStep() {
    await useAuthWxRun()
    navigateTo('/user/checkin/exercise')
}

async function getPreliminaryArchive() {
    const { results } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
    if (results) {
        archiveResults.value = results
    }
}

async function refreshArchive() {
    const { results } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
    if (results) {
        archiveResults.value = results
    }
}

function processNutritionData() {
    const totalKcal = recommendKcal.value
    nutritionList.value.forEach((item) => {
        item.current = 0
        switch (item.key) {
            case 'carbohydrates':
                item.total = Math.round((totalKcal * 0.55) / 4)
                break
            case 'fat':
                item.total = Math.round((totalKcal * 0.25) / 9)
                break
            case 'protein':
                item.total = Math.round((totalKcal * 0.20) / 4)
                break
            case 'dietaryFiber':
                item.total = 30
                break
        }
    })

    if (!mealListData.value?.results?.length) return

    const meals = mealListData.value.results
    const nutritionMap = new Map(
        nutritionList.value.map(item => [item.key, item]),
    )

    meals.forEach((meal: any) => {
        try {
            const mealContent = JSON.parse(meal.mealContent)
            if (!mealContent.items?.length) return

            mealContent.items.forEach((item: NutritionItem) => {
                for (const [key, value] of Object.entries(item)) {
                    const nutrition = nutritionMap.get(key)
                    if (nutrition && typeof value === 'number') {
                        nutrition.current = Number((nutrition.current + value).toFixed(1))
                    }
                }
            })
        } catch (error) {
            console.error('解析 mealContent 失败:', error)
        }
    })
}

function getProgress(current: number, total: number) {
    if (total === 0) return 0
    return Number((current / total).toFixed(2))
}

function handleFasting() {
    if (fastingState.value.isChallenge) {
        navigateTo('/user/checkin/fasting')
    }
}

watch(fastingState, (val) => {
    if (val.isChallenge === true) {
        setTime(val.startAt, val.endAt)
    }
}, { immediate: true, deep: true })

watch([() => recommendKcal.value, () => mealListData.value], () => {
    processNutritionData()
}, { immediate: true })

async function getCustomerIndex() {
    try {
        const { results } = await useWrapFetch<BaseResponse<CustomerIndex>>('/checkInCustomerIndex/get', {
            method: 'post',
            body: {
                kind: '5',
            },
        })

        recommendKcal.value = results?.foodKcalIndex || 0
        isHasCustomed.value = !!results?.foodKcalIndex
        loadingStillEat.value = true
        stepCalorieEnabled.value = results?.stepCalorieEnabled || false
    } catch {
        loadingStillEat.value = true
        recommendKcal.value = 0
    }
}

async function handleCalorieIntakeSave(data: { foodKcalIndex: number, stepCalorieEnabled: boolean }) {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            foodKcalIndex: data.foodKcalIndex,
            stepCalorieEnabled: data.stepCalorieEnabled,
        } })
        recommendKcal.value = data.foodKcalIndex
        stepCalorieEnabled.value = data.stepCalorieEnabled
        showStillEat.value = false
    } catch {
        showFailToast('保存失败')
    }
}

function handleStillEat() {
    if (stillEatNum.value > 0) {
        showStillEat.value = true
    }
}

async function syncStepFromWx() {
    const { wxRunData } = useWxRunData()
    const currentStep = props.stepNum

    const wxStep = Number(wxRunData?.step || 0)
    const wxStepTime = Number(wxRunData?.stepTime || 0)
    if (wxStep > currentStep && dayjs.unix(wxStepTime).isSame(dayjs(), 'day')) {
        await useWrapFetch('/checkInCustomerSport/save/foot', {
            method: 'post',
            body: {
                steps: wxStep,
                checkInDate: props.selectedDate,
            },
        })

        emit('update:stepNum', wxStep)
    }
}

async function init() {
    if (isInitialized.value) return

    await getPreliminaryArchive()
    await getCustomerIndex()
    getCaloricIntake()

    if (dayjs().isSame(dayjs(props.selectedDate), 'day')) {
        syncStepFromWx()
    }

    isInitialized.value = true
}

onMounted(() => {
    if (!isInitialized.value) {
        init()
    }
    if (!isWeightInitialized.value) {
        initWeightData()
    }
    if (!isSportInitialized.value) {
        initSportData()
    }
})

watch(
    () => props.selectedDate,
    (newDate) => {
        if (dayjs().isSame(dayjs(newDate), 'day')) {
            syncStepFromWx()
        }
    },
)

onUnmounted(() => {
    allWeightData.value = []
    isWeightDataLoaded.value = false
    allSportData.value = []
    allStepData.value = []
    isSportDataLoaded.value = false
})

defineExpose({
    refreshMeal,
    refreshSportData,
    handleClickStep,
})
</script>

<template>
    <div class="flex flex-col gap-10px">
        <!-- 体重管理卡片 -->
        <div class="flex h-144px gap-8px">
            <div class="flex flex-col flex-1 justify-around bg-white rd-10px p-12px">
                <div class="flex gap-4px items-center justify-center">
                    <div class="flex flex-col flex-1" @click="showInitialWeightSheet = true">
                        <div class="flex items-center justify-center">
                            <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="initialWeightNum"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="1"
                                />
                            </span>
                            <span class="text-#868F9C text-12px font-400 relative top-3px">kg</span>
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">初始体重</div>
                    </div>
                    <img src="@/assets/images/checkin/two-arrow.svg" alt="" srcset="" class="w-15px h-14px" />
                    <div class="flex flex-col flex-1" @click="handleShowWeightLossTarget">
                        <div
                            v-if="!['不建议减重', '建议维持'].includes(bmiTip)"
                            class="flex items-center justify-center"
                        >
                            <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="endVal"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="1"
                                />
                            </span>
                            <span class="text-#868F9C text-12px font-400 relative top-3px">kg</span>
                        </div>
                        <div
                            v-else
                            class="text-#00AC97 text-16px font-600 flex justify-center items-center"
                        >
                            {{ bmiStatus }}
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">
                            {{ bmiTip }}
                        </div>
                    </div>
                </div>
                <van-button
                    type="primary" color="#4E5969" round class="flex items-center justify-center gap-8px w-full !h-34px"
                    @click="showWeightSheet = true"
                >
                    <div class="flex items-center justify-center w-full h-full gap-8px">
                        <div class="i-custom-plus w-16px h-16px inline-block"></div>
                        <span>记录体重</span>
                    </div>
                </van-button>
            </div>
            <div
                class="w-138px flex flex-col justify-between gap-10px bg-white p-12px rd-10px"
                @click="navigateTo(weightCard?.path)"
            >
                <div class="w-full text-center text-#868F9C text-12px font-400">当前体重</div>
                <div class="flex justify-center">
                    <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                </div>
                <div class="flex justify-center items-end gap-4px">
                    <div class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                        <v-countup
                            :options="{ useGrouping: false }"
                            :end-val="weightAndHeight.weight"
                            :start-val="0"
                            :duration="1"
                            :decimal-places="1"
                        />
                    </div>
                    <div class="text-#868F9C text-12px font-400">kg</div>
                </div>
            </div>
        </div>
        <div class="h-fit flex flex-col p-12px justify-between bg-white rd-10px">
            <div class="text-#1D2229 text-14px font-600">热量收支</div>
            <div class="flex-1 flex gap-8px justify-between">
                <calorie-progress
                    v-if="loadingStillEat"
                    id="step3"
                    :progress-percent="progressPercent"
                    :still-eat-num="stillEatNum"
                    :show-arrow="true"
                    @click="handleStillEat"
                />
                <div v-else class="flex-1 relative flex flex-col items-center justify-center">
                    <shared-unified-loading size="small" :rainbow="false" />
                </div>
                <div id="step2" class="flex-1 flex flex-col gap-8px">
                    <div
                        class="w-full h-58px bg-#F5F7FA pl-8px rd-10px flex items-center gap-8px"
                        @click="navigateTo(`/user/checkin/food?date=${selectedDate}`)"
                    >
                        <div class="i-custom:checkin-health-manage-diet w-24px h-24px"></div>
                        <div class="flex flex-col">
                            <div class="text-#1D2229 text-20px font-800 flex items-center justify-center gap-5px font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="mealIntakeNum"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                                <div>
                                    Kcal
                                </div>
                            </div>
                            <div class="text-#868F9C text-10px font-400">饮食摄入 ></div>
                        </div>
                    </div>
                    <div
                        class="w-full h-58px bg-#F5F7FA pl-8px rd-10px flex items-center gap-8px"
                        @click="navigateTo(`${activityCard?.path}?date=${selectedDate}`)"
                    >
                        <div class="i-custom:checkin-health-manage-sport w-24px h-24px"></div>
                        <div class="flex flex-col">
                            <div class="text-#1D2229 text-20px font-800 flex items-center justify-center gap-5px font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="sportConsume"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                                <div>
                                    Kcal
                                </div>
                            </div>
                            <div class="text-#868F9C text-10px font-400">运动消耗 ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-165px flex justify-between gap-10px">
            <div id="step4" class="flex-1 flex flex-col gap-10px bg-white p-12px rd-10px">
                <div class="text-#1D2229 text-14px font-600">营养元素摄入</div>
                <div class="flex-1 w-full flex flex-col gap-8px">
                    <div v-for="item in nutritionList" :key="item.title" class="flex w-full gap-4px items-center justify-between">
                        <div class="text-11px text-#868F9C font-400 w-55px">{{ item.title }}</div>
                        <div class="relative flex-1 h-3px rd-6px overflow-hidden">
                            <div class="absolute inset-0 bg-#F2F4F7"></div>
                            <div
                                class="absolute inset-0 rd-6px" :style="{
                                    width: `${getProgress(item.current, item.total) * 100}%`,
                                    background: `linear-gradient(to right, ${item.gradient.from}, ${item.gradient.to})`,
                                }"
                            ></div>
                        </div>
                        <div class="text-12px text-#868F9C font-400 w-60px flex items-center"><div class="text-#1D2229">{{ item.current }}</div>/{{ item.total }}g</div>
                    </div>
                </div>
            </div>
            <div
                id="step5"
                class="w-88px h-165px rd-10px relative overflow-hidden"
                :style="{
                    background: fastingState.isChallenge ? `linear-gradient(180deg, #ffffff ${100 - progressValue}%, ${fastingState.type === 0 ? '#D8FDF8' : '#CDE4FF'} 100%)` : 'white',
                }"
            >
                <template v-if="fastingStatus">
                    <div class="absolute top-12px left-12px flex flex-col">
                        <div class="text-14px text-#1D2229 font-600">轻断食</div>
                        <div class="text-13px text-#868F9C font-400">16-8模式</div>
                    </div>
                    <div
                        class="absolute top-60% left-50% -translate-x-50% -translate-y-50% flex w-full h-full flex-col items-center justify-center"
                        @click="handleFasting"
                    >
                        <template v-if="fastingState?.isChallenge">
                            <div class="text-#1D2229 text-16px font-800 font-ddinpro">
                                {{ passedTime.hour }}:{{ passedTime.minute }}:{{ passedTime.second }}
                            </div>
                            <div class="text-#868F9C text-10px font-400">{{ fastingText }}中</div>
                        </template>
                        <template v-else>
                            <img
                                src="@/assets/images/checkin/health-manage/light-fasting.png"
                                class="w-64px h-99px"
                                @click="navigateTo('/user/checkin/fasting/start')"
                            />
                        </template>
                    </div>
                </template>
                <shared-unified-loading v-else size="small" :rainbow="false" class="absolute top-50% left-50% -translate-x-50% -translate-y-50%" />
            </div>
            <!-- <user-checkin-tools-items-card
                v-if="waterIntakeCard"
                class="w-88px! h-165px!"
                :meta="{
                    ...waterIntakeCard,
                    showQuick: false,
                    value: waterIntakeNum,
                    customCard: true,
                }"
                @click="navigateTo(waterIntakeCard.path)"
            /> -->
        </div>
        <!-- 运动管理卡片 -->
        <div class="flex h-144px gap-8px">
            <div class="flex flex-col flex-1 justify-around bg-white rd-10px p-16px">
                <div class="w-full flex items-center">
                    <div class="flex flex-col w-80px">
                        <div class="flex items-ceeter justify-center">
                            <div class="text-#1D2229 flex items-center justify-center text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="sportData?.results?.allKcal"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                            </div>
                            <div class="text-#868F9C text-12px font-400 relative top-3px">Kcal</div>
                        </div>
                        <div class="text-#868F9C text-12px font-400 mt-3px text-center">热量消耗</div>
                    </div>
                    <div class="flex flex-col w-80px">
                        <div class="flex items-center justify-center">
                            <div class="text-#1D2229 flex items-center justify-center text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="totalSportTime"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                            </div>
                            <div class="text-#868F9C text-12px font-400 relative top-3px">分钟</div>
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">运动时长</div>
                    </div>
                </div>
                <van-button
                    type="primary" color="#4E5969" round class="flex items-center justify-center gap-8px w-full !h-34px"
                    @click="navigateTo('/user/checkin/sportCalorie')"
                >
                    <div class="flex items-center justify-center w-full h-full gap-8px">
                        <div class="i-custom-plus w-16px h-16px inline-block"></div>
                        <span>记录运动</span>
                    </div>
                </van-button>
            </div>
            <div
                class="w-138px flex flex-col justify-between gap-10px bg-white p-16px rd-10px"
                @click="handleClickStep"
            >
                <div class="text-center text-#868F9C text-12px font-400">今日步数</div>
                <div class="flex justify-center">
                    <div class="i-custom:checkin-step w-32px h-32px"></div>
                </div>
                <div class="text-center text-#1D2229 text-20px font-800">
                    <v-countup
                        :options="{ useGrouping: false }"
                        :end-val="props.stepNum"
                        :start-val="0"
                        :duration="1"
                        :decimal-places="0"
                    />
                </div>
            </div>
        </div>

        <!-- 体重相关工具卡片 -->
        <div class="flex gap-10px rd-10px">
            <user-checkin-tools-items-card
                v-if="diaryCard"
                class="flex-1"
                :meta="{
                    ...diaryCard,
                    value: diaryNum,
                    title: '轻轻日记',
                }"
                @click="navigateTo(diaryCard.path)"
            />
            <user-checkin-tools-items-card
                v-if="waistCard"
                class="flex-1"
                :meta="{
                    ...waistCard,
                    value: props.waistCircumferenceNum,
                    showQuick: false,
                }"
                @click="navigateTo(waistCard.path)"
            />
        </div>

        <!-- 运动趋势图表 -->
        <time-range-chart
            title="运动记录"
            subtitle="热量(Kcal)"
            :chart-data="sportChartData"
            @tab-change="getSportTrendData"
        >
            <template #empty-state>
                <img src="@/assets/icons/checkin/sport-3.svg" class="w-32px h-32px" />
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="navigateTo('/user/checkin/sportCalorie')"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>

        <time-range-chart
            title="步数记录"
            :chart-data="stepChartData"
            @tab-change="getStepsTrendData"
        >
            <template #empty-state>
                <img src="@/assets/icons/checkin/step.svg" class="w-32px h-32px" />
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="handleClickStep"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>
        <!-- 体重趋势图表 -->
        <time-range-chart
            ref="timeRangeChartRef"
            title="目标进度"
            subtitle="体重(kg)"
            :chart-data="weightChartData"
            @tab-change="getWeightTrendData"
        >
            <template #empty-state>
                <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="showWeightSheet = true"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>

        <calorie-intake-settings
            v-model:visible="showStillEat"
            v-model:step-calorie-enabled="stepCalorieEnabled"
            :calorie-data="{
                progressPercent,
                caloricIntake,
                recommendKcal,
            }"
            :settings="{
                stepCalorieEnabled,
            }"
            :archive-results="archiveResults"
            @save="handleCalorieIntakeSave"
        />

        <!-- 体重相关子组件 -->
        <user-checkin-initial-weight
            v-model="showInitialWeightSheet"
            :initial-weight="initialWeightNum"
            @success="() => {
                refreshInitialWeight()
                showInitialWeightSheet = false
            }"
        />

        <user-checkin-weight-target
            v-model="showWeightTargetSheet"
            @success="() => {
                refreshCustomerIndex()
                showWeightTargetSheet = false
            }"
        />

        <user-checkin-weight
            v-model="showWeightSheet"
            :weight-and-height="weightAndHeight"
            @success="async () => {
                refreshArchive()
                showWeightSheet = false
                await refreshWeightData()
                if (canShowEncourageToday('weight', userInfo?.phone)) {
                    showEncouragePopup = true
                    markEncourageShownToday('weight', userInfo?.phone)
                }
            }"
        />

        <user-checkin-health-manage-weight-loss-target
            v-model="showWeightLossTarget"
            :computed-weight="computedWeight"
            :weight-and-height="weightAndHeight"
            :bmi-range-text="bmiRangeText"
            :initial-weight-loss-target="weightLossTarget"
            :initial-target-weight="targetWeightNum"
            @confirm="handleWeightLossTargetConfirm"
            @cancel="handleWeightLossTargetCancel"
        />

        <user-checkin-health-manage-encourage-popup
            v-model:show="showEncouragePopup"
            type="weight"
            :week-status="weightWeekStatus"
            :continuous-count="continuousCheckinCount"
        />
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-cell:not(.van-cell--clickable):not(.van-search__field):not(.original) .van-field__control) {
    color: #868F9C !important;
    font-size: 12px !important;
    height: 27px !important;
    line-height: 27px !important;
}
</style>
